import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Maximize2, Ruler, Home, MapPin, Shield } from "lucide-react";

// Main Component
const Property = () => {
  // Intersection observer for scroll animations
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // State to manage the active tab/panel
  const [activePanel, setActivePanel] = useState(0);

  // Data for the property panels (tabs)
  const panels = [
    {
      id: "development",
      title: "Development Overview",
      image:
        "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      features: [
        "30-Acre Premium Development",
        "RERA & DTCP Approved",
        "Gated Community",
        "Strategic Location",
      ],
      description:
        "Thoughtfully planned plotted development in North Bengaluru's fastest-growing investment corridor.",
    },
    {
      id: "plots",
      title: "Plot Specifications",
      image:
        "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      features: [
        "Multiple Plot Sizes",
        "30x40 to 60x80 sq ft",
        "Clear Title Deeds",
        "Ready for Construction",
      ],
      description:
        "Flexible plot configurations designed to meet diverse residential and investment requirements.",
    },
    {
      id: "amenities",
      title: "Community Amenities",
      image:
        "https://images.pexels.com/photos/1571468/pexels-photo-1571468.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      features: [
        "Clubhouse & Swimming Pool",
        "Sports Facilities",
        "Landscaped Gardens",
        "24/7 Security",
      ],
      description:
        "Premium community amenities designed for modern family living and recreational activities.",
    },
  ];

  // Data for development specifications
  const specifications = [
    { icon: Ruler, label: "Total Area", value: "30 Acres" },
    { icon: Home, label: "Plot Sizes", value: "30x40 to 60x80" },
    { icon: MapPin, label: "Airport Distance", value: "20 Minutes" },
    { icon: Shield, label: "Approvals", value: "RERA & DTCP" },
  ];

  // Animation variants for Framer Motion
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="property" className="py-16 sm:py-10 bg-orange-50 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-3">
            <h2 className="text-4xl md:text-5xl font-bold text-black font-sans">
              Shreyas Sunrise - Development Overview
            </h2>
            <div className="w-20 h-1 bg-primary mx-auto rounded-full" />
            <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Explore the comprehensive features and strategic advantages that make
              Shreyas Sunrise the premier plotted development investment opportunity
              in North Bengaluru's growth corridor.
            </p>
          </motion.div>

          {/* Property Panels */}
          <motion.div variants={itemVariants} className="space-y-8">
            {/* Panel Navigation */}
            <div className="flex justify-center">
              <div className="flex space-x-1 bg-white rounded-full p-2 shadow-lg border border-gray-200">
                {panels.map((panel, index) => (
                  <motion.button
                    key={panel.id}
                    onClick={() => setActivePanel(index)}
                    className={`px-4 py-2 sm:px-6 sm:py-3 rounded-full font-medium transition-all duration-300 text-sm sm:text-base ${
                      activePanel === index
                        ? "bg-primary text-white shadow-lg"
                        : "text-slate-600 hover:bg-slate-100"
                    }`}
                    whileHover={{ scale: activePanel === index ? 1 : 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {panel.title}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Active Panel Content */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activePanel}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center"
              >
                {/* Image */}
                <motion.div
                  className="relative overflow-hidden rounded-2xl shadow-2xl h-80 lg:h-full"
                  whileHover={{ scale: 1.03 }}
                  transition={{ duration: 0.4 }}
                >
                  <img
                    src={panels[activePanel].image}
                    alt={panels[activePanel].title}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                </motion.div>

                {/* Content */}
                <div className="space-y-6 bg-white rounded-2xl p-8 shadow-lg">
                  <div>
                    <h3 className="text-3xl font-bold text-slate-800 mb-3">
                      {panels[activePanel].title}
                    </h3>
                    <p className="text-lg text-slate-600 leading-relaxed">
                      {panels[activePanel].description}
                    </p>
                  </div>

                  {/* Features */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {panels[activePanel].features.map((feature, index) => (
                      <motion.div
                        key={index}
                        className="flex items-center space-x-3 p-3 bg-slate-50 rounded-lg"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                      >
                        <div className="w-2 h-2 bg-primary rounded-full" />
                        <span className="text-slate-700 font-medium">
                          {feature}
                        </span>
                      </motion.div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <motion.button
                    className="bg-primary hover:bg-primary/90 text-white px-8 py-4 rounded-lg font-bold transition-all duration-300 w-full sm:w-auto shadow-lg"
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Schedule Site Visit
                  </motion.button>
                </div>
              </motion.div>
            </AnimatePresence>
          </motion.div>

          {/* Specifications */}
          <motion.div variants={itemVariants} className="space-y-8 pt-8">
            <h3 className="text-3xl font-bold text-slate-800 text-center">
              Development Specifications
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6">
              {specifications.map((spec, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-lg p-6 text-center shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <spec.icon className="h-8 w-8 text-primary mx-auto mb-3" />
                  <div className="text-2xl font-bold text-slate-800 mb-1">
                    {spec.value}
                  </div>
                  <div className="text-sm font-medium text-slate-600">
                    {spec.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Property;

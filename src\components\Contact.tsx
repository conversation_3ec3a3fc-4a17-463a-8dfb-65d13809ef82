import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Phone,
  Mail,
  MapPin,
  Send,
  CheckCircle,
  MessageCircle,
  User,
  MessageSquare,
} from "lucide-react";

// --- ANIMATION VARIANTS ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.2 },
  },
};

const itemVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};

// --- CONTACT COMPONENT ---
const Contact = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 2000);
  };
  
  const contactInfo = [
    { icon: Phone, title: "Phone", details: ["+91-9036699799", "+91-8151884545"], color: "text-green-500 bg-green-100" },
    { icon: Mail, title: "Email", details: ["<EMAIL>"], color: "text-blue-500 bg-blue-100" },
    { icon: MapPin, title: "Location", details: ["7-8/1, 4th Main, 4th block, Kalyan Nagar, Bengaluru, Karnataka 560043"], color: "text-purple-500 bg-purple-100" },
  ];


  if (isSubmitted) {
    return (
      <section id="contact" className="py-20 bg-orange-50 flex items-center justify-center min-h-screen">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, type: "spring" }}
            className="bg-white rounded-2xl p-8 sm:p-12 shadow-2xl"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
              className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto"
            >
              <CheckCircle className="h-12 w-12 text-green-500" />
            </motion.div>
            <h2 className="text-3xl font-bold text-black font-sans mt-6">
              Thank You!
            </h2>
            <p className="text-slate-600 mt-4">
              Your inquiry has been received. Our specialist will contact you within 24 hours.
            </p>
            <motion.button
              onClick={() => setIsSubmitted(false)}
              className="mt-8 bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Send Another Message
            </motion.button>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section id="contact" className="py-16 sm:py-10 bg-primary-75 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          <div className="text-center">
            <motion.h2 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-black font-sans mb-4">
              Get In Touch
            </motion.h2>
            <motion.div variants={itemVariants} className="w-20 h-1 bg-orange-500 mx-auto mb-6" />
            <motion.p variants={itemVariants} className="text-lg text-slate-600 max-w-3xl mx-auto">
              Ready to experience luxury living? Connect with our dedicated team to schedule your private viewing or learn more about our exclusive offerings.
            </motion.p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Contact Information */}
            <motion.div variants={itemVariants} className="space-y-6">
               {contactInfo.map((info, index) => (
                <motion.div
                  key={index}
                  className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-500"
                  whileHover={{ y: -4, scale: 1.02 }}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`w-14 h-14 rounded-xl ${info.color} flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <info.icon className="h-7 w-7" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-bold text-black font-sans">{info.title}</h4>
                      {info.details.map((detail, detailIndex) => (
                        <p key={detailIndex} className="text-slate-600 text-sm leading-relaxed">
                          {detail}
                        </p>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
              <motion.div className="space-y-4 pt-4">
                 <motion.button
                    onClick={() => window.open("https://api.whatsapp.com/send?phone=919035055655&text=I am interested with shreyas properties", "_blank")}
                    className="group w-full flex items-center space-x-4 p-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.03, y: -2 }} whileTap={{ scale: 0.98 }}
                  >
                    <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center"><MessageCircle className="h-6 w-6" /></div>
                    <div className="text-left"><div className="font-bold text-lg">WhatsApp Chat</div><div className="text-sm opacity-90">Get instant responses</div></div>
                  </motion.button>
                  <motion.button
                    onClick={() => window.open("tel:+919036699799")}
                    className="group w-full flex items-center space-x-4 p-4 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.03, y: -2 }} whileTap={{ scale: 0.98 }}
                  >
                    <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center"><Phone className="h-6 w-6" /></div>
                    <div className="text-left"><div className="font-bold text-lg">Call Now</div><div className="text-sm opacity-90">Speak with our experts</div></div>
                  </motion.button>
              </motion.div>
            </motion.div>

            {/* Form */}
            <motion.div variants={itemVariants} className="bg-white rounded-3xl p-8 shadow-2xl">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-75 rounded-full mb-4">
                  <Send className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="text-2xl font-bold text-black font-sans">Send us a Message</h3>
                <p className="text-slate-500">We'll get back to you within 24 hours.</p>
              </div>

              {/* Mobile-Optimized Contact Form */}
              <form onSubmit={handleSubmit} className="form-mobile">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 xs:gap-6">
                    <input
                      type="text"
                      name="firstName"
                      placeholder="First Name *"
                      required
                      onChange={handleInputChange}
                      className="input-mobile w-full px-3 xs:px-4 py-3 border border-primary/20 rounded-xl focus:ring-2 focus:ring-primary-400 focus:border-primary-400 transition-all duration-300 bg-primary/5 text-base"
                    />
                    <input
                      type="text"
                      name="lastName"
                      placeholder="Last Name *"
                      required
                      onChange={handleInputChange}
                      className="input-mobile w-full px-3 xs:px-4 py-3 border border-primary/20 rounded-xl focus:ring-2 focus:ring-primary-400 focus:border-primary-400 transition-all duration-300 bg-primary/5 text-base"
                    />
                </div>
                <input
                  type="email"
                  name="email"
                  placeholder="Email Address *"
                  required
                  onChange={handleInputChange}
                  className="input-mobile w-full px-3 xs:px-4 py-3 border border-primary/20 rounded-xl focus:ring-2 focus:ring-primary-400 focus:border-primary-400 transition-all duration-300 bg-primary/5 text-base"
                />
                <input
                  type="tel"
                  name="phone"
                  placeholder="Phone Number *"
                  required
                  onChange={handleInputChange}
                  className="input-mobile w-full px-3 xs:px-4 py-3 border border-primary/20 rounded-xl focus:ring-2 focus:ring-primary-400 focus:border-primary-400 transition-all duration-300 bg-primary/5 text-base"
                />
                <textarea
                  name="message"
                  placeholder="Your Message..."
                  rows="4"
                  onChange={handleInputChange}
                  className="w-full px-3 xs:px-4 py-3 border border-primary/20 rounded-xl focus:ring-2 focus:ring-primary-400 focus:border-primary-400 transition-all duration-300 resize-none bg-primary/5 min-h-[120px] text-base"
                ></textarea>
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-touch w-full px-6 xs:px-8 py-4 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-xl font-accent font-bold disabled:opacity-50 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl text-sm xs:text-base"
                  whileHover={{ scale: isSubmitting ? 1 : 1.02, y: isSubmitting ? 0 : -1 }}
                  whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <Send className="h-5 w-5" />
                      <span>Send Message</span>
                    </>
                  )}
                </motion.button>
              </form>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;

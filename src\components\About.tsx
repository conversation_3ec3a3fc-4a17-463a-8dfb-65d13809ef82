import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Award, Calendar, ShieldCheck, Home } from "lucide-react";

// Animation variants for Framer Motion
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};

// About Component
const About = () => {
    const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.1 });

    const infoCards = [
        { icon: Calendar, title: "Established", value: "2010", description: "Over a decade of excellence" },
        { icon: Award, title: "RERA Approved", value: "Certified", description: "Real Estate Regulation" },
        { icon: ShieldCheck, title: "DTCP Approved", value: "Verified", description: "Town & Country Planning" },
        { icon: Home, title: "Current Project", value: "Shreyas Sunrise", description: "defining lifestyles" },
    ];

    return (
        <section id="about" className="py-16 sm:py-10 bg-orange-50 text-slate-800 font-sans">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                    ref={ref}
                    variants={containerVariants}
                    initial="hidden"
                    animate={inView ? "visible" : "hidden"}
                    className="max-w-4xl mx-auto text-center"
                >
                    <motion.div variants={itemVariants} className="space-y-12">
                        <div className="space-y-4">
                            <h2 className="text-4xl md:text-5xl font-bold text-black font-sans">
                                About Shreyas Properties
                            </h2>
                            <div className="w-20 h-1 bg-orange-500 mx-auto rounded-full" />
                        </div>
                        
                        <div className="text-lg text-slate-600 space-y-6 text-left leading-relaxed">
                            <p>
                                <strong className="text-slate-800">Shreyas Properties</strong> brings a new outlook to the business of building homes and projects of your choice, to dwell in. Our vision is to offer trusted solutions and provide ventures that will suit varying budgets and meet customer expectations. We constantly endeavor to facilitate and apply new technologies, keeping ourselves up to date with current market trends to provide you with top quality projects.
                            </p>
                            <p>
                                Our dedicated team carefully ensures that we offer attractive ventures and investment opportunities that match your budget and are also financially very viable. It has been ten years since we began and the experience we have gained only makes us want to aim higher and further push our standards of excellence.
                            </p>
                            <p>
                                <strong className="text-slate-800">Our goal</strong> is to meet expectations of our customers and ensure that they are not only investing in plots, but they are investing in happiness for themselves and their entire family. We are building a business in which the quality of our high standards, permeate into every aspect of our company.
                            </p>
                            <p>
                                <strong className="text-slate-800">Every project is a dream project for us! No deadline is too tight. No budget is too modest!</strong>
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 pt-6">
                            {infoCards.map((card, index) => (
                                <motion.div
                                    key={index}
                                    className="bg-white border border-orange-100 rounded-xl p-4 sm:p-6 text-center shadow-sm hover:shadow-lg hover:-translate-y-2 transition-all duration-300"
                                    variants={itemVariants}
                                >
                                    <card.icon className="h-8 w-8 text-orange-500 mx-auto mb-3" />
                                    <h4 className="font-bold text-slate-800 mb-1 text-base sm:text-lg">
                                        {card.title}
                                    </h4>
                                    <p className="text-sm sm:text-base text-orange-600 font-semibold">{card.value}</p>
                                    <p className="text-xs sm:text-sm text-slate-500 mt-1">{card.description}</p>
                                </motion.div>
                            ))}
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
};

export default About;

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Star, Quote, ChevronLeft, ChevronRight } from "lucide-react";

const Testimonials = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Tech Executive",
      rating: 5,
      text: "Signature Villa exceeded every expectation. The attention to detail, the breathtaking ocean views, and the seamless integration of luxury amenities created the perfect sanctuary for my family. The concierge service is unmatched.",
      image:
        "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=300",
      location: "Beverly Hills, CA",
    },
    {
      id: 2,
      name: "<PERSON>",
      title: "Investment Banker",
      rating: 5,
      text: "Having owned several luxury properties, I can confidently say that Signature Villa represents the pinnacle of oceanfront living. The architecture, the amenities, and the service quality are simply extraordinary.",
      image:
        "https://images.pexels.com/photos/91227/pexels-photo-91227.jpeg?auto=compress&cs=tinysrgb&w=300",
      location: "Manhattan, NY",
    },
    {
      id: 3,
      name: "Elena Rodriguez",
      title: "Fashion Designer",
      rating: 5,
      text: "The artistic vision and craftsmanship in every corner of this villa inspired my latest collection. It's not just a home; it's a masterpiece that perfectly balances luxury with comfort and style.",
      image:
        "https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=300",
      location: "Miami, FL",
    },
    {
      id: 4,
      name: "David Thompson",
      title: "Real Estate Mogul",
      rating: 5,
      text: "In my 30 years in luxury real estate, I've never encountered a property that combines such sophisticated design with unparalleled location and amenities. This is truly a once-in-a-lifetime investment.",
      image:
        "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=300",
      location: "Austin, TX",
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [testimonials.length]);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    );
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="testimonials" className="py-8 bg-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-8"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-4xl md:text-5xl font-bold text-black font-sans">
              What Our Clients Say
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-mobile md:text-body text-accent/90 font-montserrat max-w-2xl mx-auto">
              Hear from our distinguished clientele who have experienced the
              pinnacle of luxury living at Signature Villa.
            </p>
          </motion.div>

          {/* Testimonial Carousel */}
          <motion.div variants={itemVariants} className="relative">
            <div className="max-w-4xl mx-auto">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentTestimonial}
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ duration: 0.5 }}
                  className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-2xl"
                >
                  <div className="flex flex-col items-center space-y-8">
                    {/* Quote Icon */}
                    <motion.div
                      className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{
                        delay: 0.2,
                        type: "spring",
                        stiffness: 300,
                      }}
                    >
                      <Quote className="h-8 w-8 text-primary" />
                    </motion.div>

                    {/* Rating */}
                    <div className="flex space-x-1">
                      {[...Array(testimonials[currentTestimonial].rating)].map(
                        (_, i) => (
                          <motion.div
                            key={i}
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{
                              delay: 0.3 + i * 0.1,
                              type: "spring",
                              stiffness: 300,
                            }}
                          >
                            <Star className="h-6 w-6 text-secondary fill-secondary" />
                          </motion.div>
                        )
                      )}
                    </div>

                    {/* Testimonial Text */}
                    <motion.p
                      className="text-body-mobile md:text-body text-accent/90 font-montserrat text-center leading-relaxed max-w-3xl"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.8 }}
                    >
                      "{testimonials[currentTestimonial].text}"
                    </motion.p>

                    {/* Client Info */}
                    <motion.div
                      className="flex items-center space-x-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <motion.img
                        src={testimonials[currentTestimonial].image}
                        alt={testimonials[currentTestimonial].name}
                        className="w-16 h-16 rounded-full object-cover border-2 border-secondary"
                        whileHover={{ scale: 1.1 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      />
                      <div className="text-left">
                        <h4 className="text-black font-sans font-semibold">
                          {testimonials[currentTestimonial].name}
                        </h4>
                        <p className="text-secondary text-sm font-montserrat">
                          {testimonials[currentTestimonial].title}
                        </p>
                        <p className="text-accent/70 text-xs font-montserrat">
                          {testimonials[currentTestimonial].location}
                        </p>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Navigation Arrows */}
            <div className="absolute left-4 right-4 top-1/2 transform -translate-y-1/2 flex justify-between pointer-events-none">
              <motion.button
                onClick={prevTestimonial}
                className="p-3 rounded-full bg-white/20 hover:bg-white/30 text-accent transition-all duration-300 pointer-events-auto"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronLeft className="h-6 w-6" />
              </motion.button>

              <motion.button
                onClick={nextTestimonial}
                className="p-3 rounded-full bg-white/20 hover:bg-white/30 text-accent transition-all duration-300 pointer-events-auto"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronRight className="h-6 w-6" />
              </motion.button>
            </div>

            {/* Indicators */}
            <div className="flex justify-center space-x-3 mt-8">
              {testimonials.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`h-2 w-8 rounded-full transition-all duration-300 ${
                    index === currentTestimonial
                      ? "bg-secondary"
                      : "bg-white/40"
                  }`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.8 }}
                />
              ))}
            </div>
          </motion.div>

          {/* Statistics */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            <motion.div
              className="text-center space-y-2"
              whileHover={{ y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="text-4xl font-playfair font-bold text-secondary">
                200+
              </div>
              <div className="text-accent font-montserrat">
                Satisfied Clients
              </div>
            </motion.div>

            <motion.div
              className="text-center space-y-2"
              whileHover={{ y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="text-4xl font-playfair font-bold text-secondary">
                4.9/5
              </div>
              <div className="text-accent font-montserrat">Average Rating</div>
            </motion.div>

            <motion.div
              className="text-center space-y-2"
              whileHover={{ y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="text-4xl font-playfair font-bold text-secondary">
                100%
              </div>
              <div className="text-accent font-montserrat">
                Recommendation Rate
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;

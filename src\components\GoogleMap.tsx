
import { motion } from "framer-motion";
import { MapPin, ExternalLink, Navigation } from "lucide-react";
import locationMapImage from "../assets/locationmap.jpg";

const GoogleMap = () => {
  // Devanahalli coordinates: 13.2846° N, 77.6641° E
  const devanahalli = {
    lat: 13.2846,
    lng: 77.6641,
    name: "Shreyas Infra Projects - Devanahalli",
    address: "Devanahalli, North Bengaluru, Karnataka 562110",
  };

  // Google Maps directions URL
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${devanahalli.lat},${devanahalli.lng}&destination_place_id=ChIJX8X8X8X8X8X8X8X8X8X8X8`;

  // Google Maps place URL
  const placeUrl = `https://www.google.com/maps/place/${devanahalli.lat},${devanahalli.lng}/@${devanahalli.lat},${devanahalli.lng},15z`;

  const nearbyLandmarks = [
    {
      name: "Kempegowda International Airport",
      distance: "20 min",
      type: "Airport",
    },
    { name: "Foxconn iPhone Campus", distance: "15 min", type: "Employment" },
    { name: "SAP Labs", distance: "18 min", type: "Employment" },
    { name: "Nandi Hills", distance: "15 min", type: "Tourist Spot" },
    {
      name: "Harrow International School",
      distance: "10 min",
      type: "Education",
    },
    { name: "NH-44 Highway", distance: "5 min", type: "Highway" },
  ];

  return (
    <div className="max-w-7xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 py-6 xs:py-8 sm:py-10">
      <h2 className="text-4xl md:text-5xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-heading font-bold text-black font-sans mb-4 xs:mb-6 text-center leading-tight">
        Devanahalli Premium Plotted Development - Project Location
      </h2>

      {/* Location Map Display */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="bg-white rounded-xl xs:rounded-2xl shadow-luxury overflow-hidden border border-primary-100/50"
      >
        <div className="p-4 xs:p-6 border-b border-primary-100">
          <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between space-y-4 xs:space-y-0">
            <div className="flex items-start xs:items-center space-x-3">
              <div className="w-10 h-10 xs:w-12 xs:h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                <MapPin className="h-5 w-5 xs:h-6 xs:w-6 text-primary" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="text-lg xs:text-xl font-heading font-bold text-primary leading-tight">
                  Project Location
                </h3>
                <p className="text-charcoal-600 text-sm xs:text-base leading-relaxed">
                  {devanahalli.address}
                </p>
              </div>
            </div>
            <div className="flex flex-col xs:flex-row space-y-2 xs:space-y-0 xs:space-x-2 w-full xs:w-auto">
              <motion.a
                href={directionsUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center space-x-2 bg-primary text-white px-4 py-3 xs:py-2 rounded-xl xs:rounded-lg font-medium hover:bg-primary-600 transition-all duration-300 min-h-[44px] text-sm xs:text-base"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Navigation className="h-4 w-4 flex-shrink-0" />
                <span>Directions</span>
              </motion.a>
              <motion.a
                href={placeUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center space-x-2 border-2 border-primary text-primary px-4 py-3 xs:py-2 rounded-xl xs:rounded-lg font-medium hover:bg-primary-50 transition-all duration-300 min-h-[44px] text-sm xs:text-base"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ExternalLink className="h-4 w-4 flex-shrink-0" />
                <span className="hidden xs:inline">View on Map</span>
                <span className="xs:hidden">View Map</span>
              </motion.a>
            </div>
          </div>
        </div>

        {/* Location Map Image */}
        <div className="relative overflow-hidden bg-primary-50">
          <img
            src={locationMapImage}
            alt="Shreyas Infra Projects Devanahalli Location Map"
            className="w-full h-auto object-contain hover:scale-105 transition-transform duration-500 max-h-[300px] xs:max-h-[400px] sm:max-h-[500px] lg:max-h-none"
            loading="lazy"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
        </div>
      </motion.div>

      {/* Nearby Landmarks */}


      {/* Location Benefits */}
      {/* <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-8 text-center mt-10"
      >
        <h4 className="text-2xl font-playfair font-bold text-accent mb-4">
          Strategic Location Advantages
        </h4>
        <p className="text-accent/90 font-montserrat max-w-2xl mx-auto mb-6">
          Located in North Bengaluru's fastest-growing corridor, our Devanahalli
          project offers unparalleled connectivity to major employment hubs,
          international airport, and premium educational institutions.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-playfair font-bold text-secondary mb-2">
              20 min
            </div>
            <div className="text-accent/80 font-montserrat text-sm">
              To Airport
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-playfair font-bold text-secondary mb-2">
              ₹29,000+
            </div>
            <div className="text-accent/80 font-montserrat text-sm">
              Crores Investment Nearby
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-playfair font-bold text-secondary mb-2">
              1,20,000+
            </div>
            <div className="text-accent/80 font-montserrat text-sm">
              Jobs Being Created
            </div>
          </div>
        </div>
      </motion.div> */}
    </div>
  );
};

export default GoogleMap;

import React, { useState, useEffect } from "react";
import { ArrowDown, TrendingUp } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

// --- HERO COMPONENT ---
const Hero = () => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);

    // Hero images for the slideshow
    const heroImages = [
        {
            id: 1,
            src: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Premium Gated Development",
            title: "Premium Development"
        },
        {
            id: 2,
            src: "https://images.pexels.com/photos/1475938/pexels-photo-1475938.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Strategic Location",
            title: "Strategic Location"
        },
        {
            id: 3,
            src: "https://images.pexels.com/photos/2121121/pexels-photo-2121121.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Modern Amenities",
            title: "Modern Amenities"
        }
    ];

    // Hero content data
    const heroContent = {
        subtitle: "Shreyas Properties",
        title: "Defining Lifestyles",
        description: "Invest in a premium gated development in Bangalore's fastest-growing destination, near the International Airport.",
        highlight: "Ready for Registration | RERA Approved"
    };

    // Auto-switch images every 5 seconds
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentImageIndex((prev) => (prev + 1) % heroImages.length);
        }, 5000);
        return () => clearInterval(timer);
    }, [heroImages.length]);

    const scrollToNext = () => {
        document.getElementById("properties")?.scrollIntoView({
            behavior: "smooth"
        });
    };

    const scrollToWhyInvest = () => {
        document.getElementById("why-invest")?.scrollIntoView({
            behavior: "smooth"
        });
    };

    return (
        <section id="home" className="relative h-screen overflow-hidden font-sans mobile-safe">
            {/* Background Image Slideshow */}
            <div className="absolute inset-0">
                <AnimatePresence mode="wait">
                    <motion.div
                        key={currentImageIndex}
                        initial={{ opacity: 0, scale: 1.1 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{
                            duration: 1.5,
                            ease: "easeInOut"
                        }}
                        className="absolute inset-0 bg-cover bg-center"
                        style={{
                            backgroundImage: `url(${heroImages[currentImageIndex].src})`
                        }}
                    />
                </AnimatePresence>

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20" />
            </div>

            {/* Content */}
            <div className="relative z-10 flex items-center justify-center min-h-screen px-3 xs:px-4 sm:px-6">
                <div className="w-full max-w-5xl mx-auto text-center">
                    <motion.div
                        initial={{
                            y: 50,
                            opacity: 0
                        }}
                        animate={{
                            y: 0,
                            opacity: 1
                        }}
                        transition={{
                            duration: 0.8,
                            delay: 0.5,
                            ease: "easeOut"
                        }}
                        className="space-y-4 xs:space-y-6 sm:space-y-8">

                        {/* Subtitle */}
                        <h2 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-semibold text-orange-300 tracking-wider uppercase drop-shadow-md">
                            {heroContent.subtitle}
                        </h2>

                        {/* Main title */}
                        <h1 className="text-4xl xs:text-5xl sm:text-6xl md:text-7xl font-bold text-white drop-shadow-2xl leading-tight">
                            {heroContent.title}
                        </h1>

                        {/* Description */}
                        <p className="text-base xs:text-lg md:text-xl text-slate-200 max-w-4xl mx-auto drop-shadow-sm leading-relaxed px-2 xs:px-0">
                            {heroContent.description}
                        </p>

                        {/* Highlight badge */}
                        {heroContent.highlight && (
                            <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl px-4 xs:px-6 py-3 xs:py-4 inline-block mx-2 xs:mx-0">
                                <p className="font-semibold text-white text-sm xs:text-base">
                                    {heroContent.highlight}
                                </p>
                            </div>
                        )}

                        {/* CTA button */}
                        <div className="pt-4 xs:pt-6">
                            <motion.button
                                onClick={scrollToWhyInvest}
                                className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 xs:px-8 py-3 xs:py-4 rounded-xl font-bold text-sm xs:text-base shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 xs:space-x-3 mx-auto min-h-[44px]"
                                whileHover={{
                                    scale: 1.05,
                                    y: -2
                                }}
                                whileTap={{
                                    scale: 0.95
                                }}
                            >
                                <TrendingUp className="h-4 w-4 xs:h-5 xs:w-5" />
                                <span>Why Invest in Shreyas Sunrise?</span>
                            </motion.button>
                        </div>
                    </motion.div>
                </div>
            </div>

            {/* Image Indicators */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3">
                {heroImages.map((_, index) => (
                    <motion.button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 1 + index * 0.1 }}
                        className={`h-2 w-8 rounded-full transition-all duration-300 ${
                            index === currentImageIndex ? "bg-orange-500" : "bg-white/40"
                        }`}
                        whileHover={{ scale: 1.2 }}
                        whileTap={{ scale: 0.8 }}
                    />
                ))}
            </div>

            {/* Scroll Indicator */}
            <motion.button
                onClick={scrollToNext}
                className="absolute bottom-6 right-6 p-3 text-white/70 hover:text-white transition-colors duration-300"
                initial={{ opacity: 0 }}
                animate={{
                    opacity: 1,
                    y: [0, 10, 0]
                }}
                transition={{
                    opacity: { delay: 1.5 },
                    y: {
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                    }
                }}
                whileHover={{
                    scale: 1.1
                }}
            >
                <ArrowDown className="h-6 w-6" />
            </motion.button>
        </section>
    );
};

export default Hero;

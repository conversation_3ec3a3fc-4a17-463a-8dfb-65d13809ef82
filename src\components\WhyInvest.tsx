import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { 
  TrendingUp, 
  MapPin, 
  Building2, 
  Plane, 
  GraduationCap, 
  Factory,
  Shield,
  Award,
} from "lucide-react";

// --- ANIMATION VARIANTS ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1 },
  },
};

const itemVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

// --- WHY INVEST COMPONENT ---
const WhyInvest = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const investmentReasons = [
    { icon: TrendingUp, title: "High Appreciation Potential", description: "North Bangalore is experiencing rapid growth with significant infrastructure development and industrial expansion." },
    { icon: Plane, title: "Airport Proximity", description: "Just 20 minutes from Kempegowda International Airport, ensuring excellent connectivity." },
    { icon: Factory, title: "Industrial Hub", description: "Close to major industrial areas like Aerospace SEZ, Narasapura, and Vemgal." },
    { icon: Building2, title: "IT Corridor", description: "Proximity to AERO SEZ Devanahalli IT Park with companies like SAP Labs, Boeing, and Wipro." },
    { icon: MapPin, title: "Strategic Location", description: "Attached to State Highway 35 with excellent connectivity to Bangalore city and major hubs." },
    { icon: GraduationCap, title: "Educational Infrastructure", description: "Close to Central University of North Bengaluru and other premier educational institutions." },
    { icon: Shield, title: "RERA Approved", description: "Fully RERA and DTCP approved project ensuring legal compliance and buyer protection." },
    { icon: Award, title: "Premium Amenities", description: "World-class facilities including clubhouse, swimming pool, and landscaped gardens." }
  ];

  const developmentHighlights = [
    { title: "Bengaluru Aerospace SEZ", description: "950-acre park with investments from 56 large and mid-sized companies.", impact: "Major Employment Hub" },
    { title: "Devanahalli Business Park", description: "413-acre park expecting $2.2 billion investment over the next 3-5 years.", impact: "Economic Growth Driver" },
    { title: "International Convention Centre", description: "35-acre convention centre next to Bengaluru International Airport.", impact: "Tourism & Business Hub" },
    { title: "Infrastructure Development", description: "Devanahalli-Kolar Road widening from 2-lanes to 6-lanes for higher traffic.", impact: "Enhanced Connectivity" }
  ];

  return (
    <section id="why-invest" className="py-16 sm:py-10 bg-primary-75 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          <div className="text-center mb-12">
            <motion.h2 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-black font-sans mb-4">
              Why Invest in Shreyas Sunrise?
            </motion.h2>
            <motion.div variants={itemVariants} className="w-20 h-1 bg-orange-500 mx-auto mb-6" />
            <motion.p variants={itemVariants} className="text-lg text-slate-600 max-w-3xl mx-auto">
              Discover why Shreyas Sunrise represents one of the most promising investment opportunities in North Bangalore's rapidly developing corridor.
            </motion.p>
          </div>

          {/* Investment Reasons Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
          >
            {investmentReasons.map((reason, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-white rounded-xl p-6 shadow-md hover:shadow-xl hover:-translate-y-2 transition-all duration-300 group"
              >
                <div className="bg-primary-75 text-orange-600 w-12 h-12 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 group-hover:bg-orange-500 group-hover:text-white transition-all duration-300">
                  <reason.icon size={24} />
                </div>
                <h3 className="font-bold text-lg text-slate-800 mb-2">
                  {reason.title}
                </h3>
                <p className="text-sm text-slate-600 leading-relaxed">
                  {reason.description}
                </p>
              </motion.div>
            ))}
          </motion.div>

          {/* Development Highlights */}
          <motion.div
            variants={containerVariants}
            className="bg-white rounded-2xl p-8 lg:p-12 shadow-lg"
          >
            <motion.h3
              variants={itemVariants}
              className="text-3xl font-bold text-slate-800 mb-8 text-center"
            >
              Key Developments Nearby
            </motion.h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {developmentHighlights.map((highlight, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="border-l-4 border-orange-500 pl-6"
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-bold text-lg text-slate-700">
                      {highlight.title}
                    </h4>
                    <span className="bg-primary-75 text-orange-600 text-xs px-3 py-1 rounded-full font-semibold">
                      {highlight.impact}
                    </span>
                  </div>
                  <p className="text-sm text-slate-600 leading-relaxed">
                    {highlight.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Call to Action */}
          <motion.div
            variants={itemVariants}
            className="text-center mt-16"
          >
            <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-8 lg:p-12 text-white shadow-2xl">
              <h3 className="text-3xl font-bold mb-4">
                Ready to Invest in Your Future?
              </h3>
              <p className="text-lg mb-6 opacity-90 max-w-2xl mx-auto">
                Don't miss this opportunity to be part of North Bangalore's growth story. Contact us today for site visits and detailed project information.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white text-orange-600 px-8 py-3 rounded-lg font-bold hover:bg-orange-50 transition-colors duration-300 shadow-lg"
                >
                  Book Free Site Visit
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-bold hover:bg-white hover:text-orange-600 transition-all duration-300"
                >
                  Download Brochure
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyInvest;
